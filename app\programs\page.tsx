import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import {
  Fish,
  Heart,
  TrendingUp,
  Users,
  Truck,
  Building,
  Wrench,
  GraduationCap,
  Target,
  Clock,
  CheckCircle,
  DollarSign,
  Zap,
  Sparkles,
  ArrowRight,
  BarChart3,
  Lightbulb,
} from "lucide-react"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"
import ScrollReveal from "@/components/scroll-reveal"
import GlassCard from "@/components/glass-card"
import FloatingElements from "@/components/floating-elements"

export default function ProgramsPage() {
  const currentPrograms = [
    {
      title: "Cage Fish Farming",
      description:
        "Comprehensive training and support for women in sustainable cage fish farming techniques on Lake Victoria",
      icon: Fish,
      color: "blue",
      details: [
        "Training in cage construction and maintenance",
        "Fish feeding and health management",
        "Water quality monitoring",
        "Harvest and post-harvest handling",
        "Record keeping and business management",
      ],
      beneficiaries: "1,400+ women",
      duration: "Ongoing since 2019",
      impact: "394.44 tons of fish produced",
      stats: { completion: 95, satisfaction: 98 },
    },
    {
      title: "Day Care Services",
      description: "Providing safe childcare services for working mothers participating in aquaculture activities",
      icon: Heart,
      color: "green",
      details: [
        "Safe and nurturing environment for children",
        "Qualified childcare providers",
        "Educational activities and play",
        "Nutritious meals and snacks",
        "Health monitoring and basic care",
      ],
      beneficiaries: "500+ children",
      duration: "Daily operations",
      impact: "Enables mothers to focus on work",
      stats: { completion: 100, satisfaction: 96 },
    },
    {
      title: "Value Chain Capacity Building",
      description: "Comprehensive training along the fish value chain from production to marketing",
      icon: TrendingUp,
      color: "teal",
      details: [
        "Production techniques and best practices",
        "Processing and value addition",
        "Quality control and food safety",
        "Marketing and sales strategies",
        "Financial literacy and business skills",
      ],
      beneficiaries: "1,400+ women",
      duration: "Continuous training",
      impact: "Enhanced skills across value chain",
      stats: { completion: 88, satisfaction: 94 },
    },
  ]

  const futurePrograms = [
    {
      title: "Land-based Recirculating Aquaculture System (RAS)",
      description: "Advanced aquaculture system to overcome natural water mixing challenges in Lake Victoria",
      priority: "High",
      timeline: "2024-2025",
      investment: "Seeking funding",
      icon: Zap,
      color: "blue",
      benefits: [
        "Year-round fish production",
        "Better water quality control",
        "Higher production efficiency",
        "Reduced environmental risks",
      ],
    },
    {
      title: "Fish Processing Plant",
      description: "Value addition facility to process fish into various products for local and export markets",
      priority: "High",
      timeline: "2025-2026",
      investment: "Partnership needed",
      icon: Building,
      color: "green",
      benefits: ["Higher profit margins", "Extended shelf life", "Market diversification", "Job creation"],
    },
    {
      title: "Innovation Hub",
      description: "Technology center for aquaculture research and digital solutions",
      priority: "Medium",
      timeline: "2025-2027",
      investment: "Seeking support",
      icon: Lightbulb,
      color: "purple",
      benefits: [
        "Research and development",
        "Digital monitoring systems",
        "Innovation incubation",
        "Knowledge sharing platform",
      ],
    },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 relative overflow-hidden">
      <FloatingElements />
      <Navbar />

      {/* Hero Section */}
      <section className="relative h-64 flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-teal-900/90 via-blue-800/80 to-green-800/90" />
          <div
            className="absolute inset-0 bg-cover bg-center transform scale-110"
            style={{
              backgroundImage: `url('/images/webImages/Q6.jpg')`,
            }}
          />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center max-w-3xl mx-auto text-white">
            <ScrollReveal>
              <Badge className="bg-white/20 backdrop-blur-sm text-white border-white/30 px-4 py-2 text-sm font-semibold mb-4">
                Our Programs
              </Badge>
            </ScrollReveal>

            <ScrollReveal delay={200}>
              <h1 className="text-3xl lg:text-4xl font-serif font-bold mb-4 leading-tight">
                Our Programs
              </h1>
            </ScrollReveal>

            <ScrollReveal delay={400}>
              <p className="text-lg text-teal-100 leading-relaxed max-w-2xl mx-auto">
                Discover our comprehensive programs designed to empower women through sustainable aquaculture.
              </p>
            </ScrollReveal>

            <ScrollReveal delay={600}>
              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <Button
                  size="lg"
                  className="bg-white text-teal-900 hover:bg-teal-50 px-8 py-4 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group"
                >
                  <BarChart3 className="mr-2 h-5 w-5 group-hover:animate-pulse" />
                  View Programs
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white/30 text-white hover:bg-white/10 backdrop-blur-sm px-8 py-4 rounded-full bg-transparent"
                >
                  <Sparkles className="mr-2 h-5 w-5" />
                  Future Initiatives
                </Button>
              </div>
            </ScrollReveal>
          </div>
        </div>
      </section>

      {/* Program Tabs - Enhanced */}
      <section className="py-32 relative">
        <div className="container mx-auto px-4">
          <Tabs defaultValue="current" className="w-full">
            <div className="flex justify-center mb-16">
              <TabsList className="grid w-full max-w-md grid-cols-3 bg-white/80 backdrop-blur-sm border border-gray-200 rounded-2xl p-2">
                <TabsTrigger
                  value="current"
                  className="rounded-xl data-[state=active]:bg-blue-600 data-[state=active]:text-white"
                >
                  Current Programs
                </TabsTrigger>
                <TabsTrigger
                  value="future"
                  className="rounded-xl data-[state=active]:bg-green-600 data-[state=active]:text-white"
                >
                  Future Initiatives
                </TabsTrigger>
                <TabsTrigger
                  value="support"
                  className="rounded-xl data-[state=active]:bg-teal-600 data-[state=active]:text-white"
                >
                  Support Services
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Current Programs - Enhanced Layout */}
            <TabsContent value="current" className="space-y-16">
              <ScrollReveal>
                <div className="text-center mb-16">
                  <Badge className="bg-gradient-to-r from-blue-100 to-teal-100 text-blue-800 border-0 px-6 py-2 text-sm font-semibold mb-6">
                    Active Programs
                  </Badge>
                  <h2 className="text-5xl font-serif font-bold text-gray-900 mb-6">
                    Our Core Programs Currently
                    <span className="block bg-gradient-to-r from-blue-600 to-teal-600 bg-clip-text text-transparent">
                      Transforming Women's Lives
                    </span>
                  </h2>
                </div>
              </ScrollReveal>

              <div className="space-y-16">
                {currentPrograms.map((program, index) => (
                  <ScrollReveal key={index} delay={index * 200}>
                    <div className="relative group">
                      <div className="absolute inset-0 bg-gradient-to-br from-white to-gray-50 rounded-3xl shadow-2xl group-hover:shadow-3xl transition-all duration-500 transform group-hover:scale-[1.02]" />
                      <div className="relative grid lg:grid-cols-2 gap-12 p-12">
                        <div className="space-y-8">
                          <div className="flex items-center space-x-6">
                            <div
                              className={`w-24 h-24 bg-gradient-to-br from-${program.color}-100 to-${program.color}-200 rounded-3xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-6 transition-all duration-500`}
                            >
                              <program.icon className={`h-12 w-12 text-${program.color}-600`} />
                            </div>
                            <div>
                              <h3 className="text-3xl font-serif font-bold text-gray-900 mb-2">{program.title}</h3>
                              <p className="text-lg text-gray-600">{program.description}</p>
                            </div>
                          </div>

                          <div className="space-y-6">
                            <h4 className="text-xl font-serif font-semibold text-gray-900">Program Components:</h4>
                            <div className="grid gap-3">
                              {program.details.map((detail, idx) => (
                                <div key={idx} className="flex items-start space-x-3">
                                  <CheckCircle className="h-5 w-5 text-green-600 mt-1 flex-shrink-0" />
                                  <span className="text-gray-600">{detail}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>

                        <div className="space-y-8">
                          <div className="grid grid-cols-2 gap-6">
                            <GlassCard className="p-6 text-center">
                              <div className={`text-3xl font-bold text-${program.color}-600 mb-2`}>
                                {program.stats.completion}%
                              </div>
                              <div className="text-sm text-gray-600">Completion Rate</div>
                            </GlassCard>
                            <GlassCard className="p-6 text-center">
                              <div className={`text-3xl font-bold text-${program.color}-600 mb-2`}>
                                {program.stats.satisfaction}%
                              </div>
                              <div className="text-sm text-gray-600">Satisfaction</div>
                            </GlassCard>
                          </div>

                          <div className="space-y-6">
                            <div className="flex items-center space-x-4">
                              <Users className="h-6 w-6 text-blue-600" />
                              <div>
                                <p className="font-semibold text-gray-900">Beneficiaries</p>
                                <p className="text-gray-600">{program.beneficiaries}</p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-4">
                              <Clock className="h-6 w-6 text-green-600" />
                              <div>
                                <p className="font-semibold text-gray-900">Duration</p>
                                <p className="text-gray-600">{program.duration}</p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-4">
                              <Target className="h-6 w-6 text-teal-600" />
                              <div>
                                <p className="font-semibold text-gray-900">Impact</p>
                                <p className="text-gray-600">{program.impact}</p>
                              </div>
                            </div>
                          </div>

                          <Button
                            className={`w-full bg-gradient-to-r from-${program.color}-600 to-${program.color}-700 hover:from-${program.color}-700 hover:to-${program.color}-800 text-white rounded-full py-3 group`}
                          >
                            Learn More About This Program
                            <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </ScrollReveal>
                ))}
              </div>
            </TabsContent>

            {/* Future Programs - Creative Cards */}
            <TabsContent value="future" className="space-y-12">
              <ScrollReveal>
                <div className="text-center mb-16">
                  <Badge className="bg-gradient-to-r from-green-100 to-purple-100 text-green-800 border-0 px-6 py-2 text-sm font-semibold mb-6">
                    Future Initiatives
                  </Badge>
                  <h2 className="text-5xl font-serif font-bold text-gray-900 mb-6">
                    Planned Programs to
                    <span className="block bg-gradient-to-r from-green-600 to-purple-600 bg-clip-text text-transparent">
                      Expand Our Impact
                    </span>
                  </h2>
                </div>
              </ScrollReveal>

              <div className="grid lg:grid-cols-3 gap-8">
                {futurePrograms.map((program, index) => (
                  <ScrollReveal key={index} delay={index * 200} direction="up">
                    <div className="relative group h-full">
                      <div className="absolute inset-0 bg-gradient-to-br from-white to-gray-50 rounded-3xl shadow-2xl group-hover:shadow-3xl transition-all duration-500 transform group-hover:-rotate-1" />
                      <div className="relative p-8 h-full flex flex-col">
                        <div className="flex items-center justify-between mb-6">
                          <div
                            className={`w-16 h-16 bg-gradient-to-br from-${program.color}-100 to-${program.color}-200 rounded-2xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-12 transition-all duration-500`}
                          >
                            <program.icon className={`h-8 w-8 text-${program.color}-600`} />
                          </div>
                          <Badge
                            className={`${program.priority === "High" ? "bg-red-100 text-red-800" : "bg-yellow-100 text-yellow-800"}`}
                          >
                            {program.priority} Priority
                          </Badge>
                        </div>

                        <h3 className="text-2xl font-serif font-bold text-gray-900 mb-4">{program.title}</h3>
                        <p className="text-gray-600 mb-6 leading-relaxed flex-grow">{program.description}</p>

                        <div className="space-y-4 mb-6">
                          <div className="flex items-center space-x-3">
                            <Clock className="h-5 w-5 text-blue-600" />
                            <div>
                              <p className="font-semibold text-gray-900 text-sm">Timeline</p>
                              <p className="text-gray-600 text-sm">{program.timeline}</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-3">
                            <DollarSign className="h-5 w-5 text-green-600" />
                            <div>
                              <p className="font-semibold text-gray-900 text-sm">Investment Status</p>
                              <p className="text-gray-600 text-sm">{program.investment}</p>
                            </div>
                          </div>
                        </div>

                        <div className="mb-6">
                          <h4 className="font-serif font-semibold text-gray-900 mb-3">Expected Benefits:</h4>
                          <div className="space-y-2">
                            {program.benefits.map((benefit, idx) => (
                              <div key={idx} className="flex items-start space-x-2">
                                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                                <span className="text-gray-600 text-sm">{benefit}</span>
                              </div>
                            ))}
                          </div>
                        </div>

                        <Button
                          className={`w-full bg-gradient-to-r from-${program.color}-600 to-${program.color}-700 hover:from-${program.color}-700 hover:to-${program.color}-800 text-white rounded-full group`}
                        >
                          <Sparkles className="mr-2 h-4 w-4 group-hover:animate-spin" />
                          Express Interest
                        </Button>
                      </div>
                    </div>
                  </ScrollReveal>
                ))}
              </div>
            </TabsContent>

            {/* Support Services */}
            <TabsContent value="support" className="space-y-12">
              <ScrollReveal>
                <div className="text-center mb-16">
                  <Badge className="bg-gradient-to-r from-teal-100 to-blue-100 text-teal-800 border-0 px-6 py-2 text-sm font-semibold mb-6">
                    Support Services
                  </Badge>
                  <h2 className="text-5xl font-serif font-bold text-gray-900 mb-6">
                    Essential Services That
                    <span className="block bg-gradient-to-r from-teal-600 to-blue-600 bg-clip-text text-transparent">
                      Enable Our Programs
                    </span>
                  </h2>
                </div>
              </ScrollReveal>

              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                {[
                  {
                    icon: Truck,
                    title: "Refrigerated Transport",
                    description: "Refrigerated truck for maintaining fish quality during transport to markets",
                    status: "Active",
                  },
                  {
                    icon: Building,
                    title: "Accommodation Facilities",
                    description: "Housing for women who come to work from distant areas",
                    status: "Active",
                  },
                  {
                    icon: Wrench,
                    title: "Equipment & Tools",
                    description: "Provision of necessary tools and equipment for fish farming operations",
                    status: "Active",
                  },
                  {
                    icon: GraduationCap,
                    title: "Training Facilities",
                    description: "Dedicated spaces for conducting training sessions and workshops",
                    status: "Active",
                  },
                ].map((service, index) => (
                  <ScrollReveal key={index} delay={index * 150} direction="up">
                    <GlassCard className="p-8 text-center group h-full">
                      <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-teal-200 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 group-hover:rotate-6 transition-all duration-300">
                        <service.icon className="h-8 w-8 text-blue-600" />
                      </div>
                      <h3 className="text-xl font-serif font-bold text-gray-900 mb-4">{service.title}</h3>
                      <p className="text-gray-600 mb-4 leading-relaxed flex-grow">{service.description}</p>
                      <Badge className="bg-green-100 text-green-800">{service.status}</Badge>
                    </GlassCard>
                  </ScrollReveal>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-teal-600 via-blue-600 to-green-600" />
        <div className="absolute inset-0 bg-black/20" />
        <div className="container mx-auto px-4 relative z-10">
          <ScrollReveal>
            <div className="text-center text-white">
              <h2 className="text-5xl font-serif font-bold mb-8">Support Our Programs</h2>
              <p className="text-xl mb-12 max-w-4xl mx-auto leading-relaxed opacity-90">
                Help us expand our programs and reach more women. Your support can make a lasting difference in
                communities across Uganda.
              </p>
              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <Button
                  size="lg"
                  className="bg-white text-teal-600 hover:bg-gray-100 px-8 py-4 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group"
                >
                  <Heart className="mr-2 h-5 w-5 group-hover:animate-pulse" />
                  Fund a Program
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white/30 text-white hover:bg-white/10 backdrop-blur-sm px-8 py-4 rounded-full transition-all duration-300 hover:scale-105 bg-transparent"
                >
                  <Users className="mr-2 h-5 w-5" />
                  Become a Partner
                </Button>
              </div>
            </div>
          </ScrollReveal>
        </div>
      </section>

      <Footer />
    </div>
  )
}
