import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter, Playfair_Display } from "next/font/google"
import "./globals.css"

const inter = Inter({ subsets: ["latin"] })
const playfair = Playfair_Display({
  subsets: ["latin"],
  variable: "--font-serif",
  display: "swap",
})

export const metadata: Metadata = {
  title: "WAIB - Women Aquaculture Initiative Bugiri",
  description:
    "Empowering women through sustainable aquaculture in Bugiri District, Uganda. Transforming lives and communities through cage fish farming.",
  keywords:
    "women empowerment, aquaculture, fish farming, Uganda, Bugiri, sustainable development, economic empowerment",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={`${inter.className} ${playfair.variable}`}>{children}</body>
    </html>
  )
}
