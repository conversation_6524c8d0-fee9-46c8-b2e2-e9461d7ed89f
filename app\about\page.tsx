import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Target, Eye, Heart, Users, Award, TrendingUp, ArrowRight, Sparkles, Zap, Globe } from "lucide-react"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"
import ScrollReveal from "@/components/scroll-reveal"
import GlassCard from "@/components/glass-card"
import FloatingElements from "@/components/floating-elements"

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 relative overflow-hidden">
      <FloatingElements />
      <Navbar />

      {/* Hero Section with Parallax */}
      <section className="relative h-64 flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-900/90 via-blue-800/80 to-green-800/90" />
          <div
            className="absolute inset-0 bg-cover bg-center transform scale-110"
            style={{
              backgroundImage: `url('/images/webImages/Q5.jpg')`,
            }}
          />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center max-w-3xl mx-auto text-white">
            <ScrollReveal>
              <Badge className="bg-white/20 backdrop-blur-sm text-white border-white/30 px-4 py-2 text-sm font-semibold mb-4">
                About WAIB
              </Badge>
            </ScrollReveal>

            <ScrollReveal delay={200}>
              <h1 className="text-3xl lg:text-4xl font-serif font-bold mb-4 leading-tight">
                About Us
              </h1>
            </ScrollReveal>

            <ScrollReveal delay={400}>
              <p className="text-lg text-blue-100 leading-relaxed max-w-2xl mx-auto">
                Discover the story behind WAIB's mission to empower women through sustainable aquaculture practices.
              </p>
            </ScrollReveal>
          </div>
        </div>
      </section>

      {/* Timeline Story Section */}
      <section className="py-32 relative">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <div className="text-center mb-20">
              <Badge className="bg-gradient-to-r from-blue-100 to-green-100 text-blue-800 border-0 px-6 py-2 text-sm font-semibold mb-6">
                Our Journey
              </Badge>
              <h2 className="text-5xl font-serif font-bold text-gray-900 mb-6">
                The Story Behind
                <span className="block bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
                  Our Impact
                </span>
              </h2>
            </div>
          </ScrollReveal>

          {/* Interactive Timeline */}
          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-blue-500 to-green-500 rounded-full" />

            {[
              {
                year: "2019",
                title: "The Beginning",
                description:
                  "WAIB began as an ambitious project with UGX 2.8 Billion funding to empower 1,400 women over three years.",
                icon: Sparkles,
                color: "blue",
                side: "left",
              },
              {
                year: "2020",
                title: "Infrastructure Development",
                description: "Established operations at Wakawaka Landing Site with modern facilities and equipment.",
                icon: Target,
                color: "green",
                side: "right",
              },
              {
                year: "2021",
                title: "First Harvest Success",
                description:
                  "Achieved significant fish production milestones and began dividend payments to beneficiaries.",
                icon: Award,
                color: "teal",
                side: "left",
              },
              {
                year: "2024",
                title: "Recognition & Growth",
                description:
                  "Received presidential recognition and expanded partnerships with international organizations.",
                icon: Globe,
                color: "purple",
                side: "right",
              },
            ].map((milestone, index) => (
              <ScrollReveal key={index} delay={index * 200}>
                <div className={`flex items-center mb-16 ${milestone.side === "right" ? "flex-row-reverse" : ""}`}>
                  <div className={`w-1/2 ${milestone.side === "right" ? "pl-12" : "pr-12"}`}>
                    <GlassCard className="p-8 group">
                      <div className="flex items-center space-x-4 mb-4">
                        <div
                          className={`w-16 h-16 bg-gradient-to-br from-${milestone.color}-100 to-${milestone.color}-200 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}
                        >
                          <milestone.icon className={`h-8 w-8 text-${milestone.color}-600`} />
                        </div>
                        <div>
                          <div className={`text-3xl font-bold text-${milestone.color}-600`}>{milestone.year}</div>
                          <h3 className="text-xl font-serif font-bold text-gray-900">{milestone.title}</h3>
                        </div>
                      </div>
                      <p className="text-gray-600 leading-relaxed">{milestone.description}</p>
                    </GlassCard>
                  </div>

                  <div className="relative z-10">
                    <div className={`w-6 h-6 bg-${milestone.color}-500 rounded-full border-4 border-white shadow-lg`} />
                  </div>

                  <div className="w-1/2" />
                </div>
              </ScrollReveal>
            ))}
          </div>
        </div>
      </section>

      {/* Mission, Vision, Goal - Creative Layout */}
      <section className="py-32 relative">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50/50 to-green-50/50" />
        <div className="container mx-auto px-4 relative">
          <ScrollReveal>
            <div className="text-center mb-20">
              <Badge className="bg-gradient-to-r from-green-100 to-teal-100 text-green-800 border-0 px-6 py-2 text-sm font-semibold mb-6">
                Our Foundation
              </Badge>
              <h2 className="text-5xl font-serif font-bold text-gray-900 mb-6">
                The Principles That
                <span className="block bg-gradient-to-r from-green-600 to-teal-600 bg-clip-text text-transparent">
                  Guide Our Work
                </span>
              </h2>
            </div>
          </ScrollReveal>

          <div className="grid lg:grid-cols-3 gap-8">
            {[
              {
                icon: Target,
                title: "Our Mission",
                description:
                  "To increase women economic empowerment through cage fish farming and create market linkages through increased partnerships.",
                color: "blue",
                delay: 0,
              },
              {
                icon: Eye,
                title: "Our Vision",
                description:
                  "Transformed poor rural women from vulnerability to an economic empowerment space, turning WAIB into a center of excellence by 2030.",
                color: "green",
                delay: 200,
              },
              {
                icon: Heart,
                title: "Our Goal",
                description: "Women have income security, decent work and economic autonomy by 2025.",
                color: "teal",
                delay: 400,
              },
            ].map((item, index) => (
              <ScrollReveal key={index} delay={item.delay} direction="up">
                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-br from-white to-gray-50 rounded-3xl shadow-2xl group-hover:shadow-3xl transition-all duration-500 transform group-hover:-rotate-1" />
                  <div className="relative p-10 text-center">
                    <div
                      className={`w-24 h-24 bg-gradient-to-br from-${item.color}-100 to-${item.color}-200 rounded-3xl flex items-center justify-center mx-auto mb-8 group-hover:scale-110 group-hover:rotate-12 transition-all duration-500`}
                    >
                      <item.icon className={`h-12 w-12 text-${item.color}-600`} />
                    </div>
                    <h3 className="text-2xl font-serif font-bold text-gray-900 mb-6">{item.title}</h3>
                    <p className="text-gray-600 leading-relaxed text-lg">{item.description}</p>
                  </div>
                </div>
              </ScrollReveal>
            ))}
          </div>
        </div>
      </section>

      {/* Achievements Grid */}
      <section className="py-32 relative">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <div className="text-center mb-20">
              <Badge className="bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-800 border-0 px-6 py-2 text-sm font-semibold mb-6">
                Key Achievements
              </Badge>
              <h2 className="text-5xl font-serif font-bold text-gray-900 mb-6">
                Transformative Impact Across
                <span className="block bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent">
                  Multiple Dimensions
                </span>
              </h2>
            </div>
          </ScrollReveal>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: TrendingUp,
                title: "Transformative Impact",
                description: "Women started small businesses and gained economic independence",
                color: "blue",
              },
              {
                icon: Users,
                title: "Poverty Alleviation",
                description: "Women now offer paid labor and contribute to household income",
                color: "green",
              },
              {
                icon: Award,
                title: "Food Security",
                description: "Increased agricultural productivity and enhanced nutrition security",
                color: "teal",
              },
              {
                icon: Heart,
                title: "Gender Relations",
                description: "Improved gender relations and reduced domestic violence",
                color: "purple",
              },
              {
                icon: Target,
                title: "Cultural Shift",
                description: "Women now work on the lake - a significant cultural transformation",
                color: "orange",
              },
              {
                icon: Zap,
                title: "Scalable Model",
                description: "Effective and scalable approach to women's empowerment",
                color: "pink",
              },
            ].map((achievement, index) => (
              <ScrollReveal key={index} delay={index * 100} direction="up">
                <GlassCard className="p-8 group h-full">
                  <div className="flex items-start space-x-4">
                    <div
                      className={`w-16 h-16 bg-gradient-to-br from-${achievement.color}-100 to-${achievement.color}-200 rounded-2xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 group-hover:rotate-6 transition-all duration-300`}
                    >
                      <achievement.icon className={`h-8 w-8 text-${achievement.color}-600`} />
                    </div>
                    <div>
                      <h3 className="text-xl font-serif font-bold text-gray-900 mb-3">{achievement.title}</h3>
                      <p className="text-gray-600 leading-relaxed">{achievement.description}</p>
                    </div>
                  </div>
                </GlassCard>
              </ScrollReveal>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 via-blue-700 to-green-600" />
        <div className="absolute inset-0 bg-black/20" />
        <div className="container mx-auto px-4 relative z-10">
          <ScrollReveal>
            <div className="text-center text-white">
              <h2 className="text-5xl font-serif font-bold mb-8">Be Part of Our Journey</h2>
              <p className="text-xl mb-12 max-w-4xl mx-auto leading-relaxed opacity-90">
                Join us in creating lasting change and empowering more women through sustainable aquaculture. Together,
                we can build a brighter future for communities in Uganda.
              </p>
              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <Button
                  size="lg"
                  className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group"
                >
                  <Heart className="mr-2 h-5 w-5 group-hover:animate-pulse" />
                  Partner with Us
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white/30 text-white hover:bg-white/10 backdrop-blur-sm px-8 py-4 rounded-full transition-all duration-300 hover:scale-105 bg-transparent"
                >
                  <Globe className="mr-2 h-5 w-5" />
                  Support Our Mission
                </Button>
              </div>
            </div>
          </ScrollReveal>
        </div>
      </section>

      <Footer />
    </div>
  )
}
