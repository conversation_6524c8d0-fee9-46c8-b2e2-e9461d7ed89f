import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Calendar, MapPin, Eye, Download, Share2, Heart, Globe } from "lucide-react"
import Image from "next/image"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"

export default function GalleryPage() {
  const galleryCategories = [
    {
      id: "dignitary-visits",
      title: "Dignitary Visits",
      description: "High-profile visits from government officials and international partners",
      count: 12,
    },
    {
      id: "community-work",
      title: "Community Engagement",
      description: "Women at work and community activities",
      count: 24,
    },
    {
      id: "facilities",
      title: "Our Facilities",
      description: "Aquaculture facilities, equipment, and infrastructure",
      count: 18,
    },
    {
      id: "achievements",
      title: "Achievements & Awards",
      description: "Recognition ceremonies and milestone celebrations",
      count: 8,
    },
    {
      id: "training",
      title: "Training Sessions",
      description: "Capacity building and skills development programs",
      count: 15,
    },
  ]

  const dignitaryVisits = [
    {
      title: "Commissioner for Fisheries Launch",
      description: "Commissioner for Fisheries officially launches the farm on June 25th, 2019",
      date: "June 25, 2019",
      location: "Wakawaka Landing Site",
      image: "/images/webImages/image (3).png",
      category: "Government Visit",
    },
    {
      title: "Rt. Hon. Rebecca Alitwala Kadaga Visit",
      description: "Right Honorable Rebecca Alitwala Kadaga commissions the land base structure",
      date: "November 21, 2020",
      location: "WAIB Facilities",
      image: "/images/webImages/generation-fe6d168d-1308-4e70-b0f5-71375a56c073.png",
      category: "Government Visit",
    },
    {
      title: "Minister of State for Fisheries Visit",
      description: "Honorable Helen Adoa - Minister of State for Fisheries visits the facility",
      date: "March 29, 2021",
      location: "Wakawaka Landing Site",
      image: "/images/webImages/Q10 - Copy.jpg",
      category: "Government Visit",
    },
    {
      title: "Swedish Ambassador & UN Women Visit",
      description: "Ambassador of Sweden Maria and UN Women Uganda Country Representative visit",
      date: "September 30, 2022",
      location: "Bugiri District",
      image: "/images/webImages/asd.png",
      category: "International Visit",
    },
    {
      title: "UN Women Monitoring Visit",
      description: "UN Women Operations Officer Benson and Program Analyst Patricia monitoring visit",
      date: "2023",
      location: "WAIB Farm",
      image: "/images/webImages/Q8 - Copy.jpg",
      category: "International Visit",
    },
  ]

  const communityWork = [
    {
      title: "Women at Fish Cages",
      description: "Women working at the fish cages on Lake Victoria",
      image: "/images/webImages/Q9.jpg",
      category: "Daily Operations",
    },
    {
      title: "Day Care Services",
      description: "Children being cared for while mothers work",
      image: "/images/webImages/Q10.jpg",
      category: "Support Services",
    },
    {
      title: "Fish Harvesting",
      description: "Community members participating in fish harvesting activities",
      image: "/images/webImages/Image_fx (2).jpg",
      category: "Production",
    },
    {
      title: "Training Session",
      description: "Women participating in value chain training",
      image: "/images/webImages/Image_fx (3).jpg",
      category: "Capacity Building",
    },
    {
      title: "Market Activities",
      description: "Women selling fish at local markets",
      image: "/images/webImages/Image_fx (5).jpg",
      category: "Marketing",
    },
    {
      title: "Community Meeting",
      description: "Community gathering and planning session",
      image: "/images/webImages/Image_fx (9).jpg",
      category: "Community Engagement",
    },
  ]

  const facilities = [
    {
      title: "Fish Cages on Lake Victoria",
      description: "Modern fish cages installed on Lake Victoria for sustainable aquaculture",
      image: "/images/webImages/Image_fx (17).jpg",
      category: "Production Facility",
    },
    {
      title: "Refrigerated Transport Truck",
      description: "Refrigerated truck for maintaining fish quality during transport to markets",
      image: "/images/webImages/Q4 - Copy.jpg",
      category: "Transport",
    },
    {
      title: "Land Base Structure",
      description: "Administrative and processing facilities on land",
      image: "/images/webImages/Q8 - Copy.jpg",
      category: "Infrastructure",
    },
    {
      title: "Training Facilities",
      description: "Dedicated spaces for conducting training and workshops",
      image: "/images/webImages/Q9 - Copy.jpg",
      category: "Education",
    },
    {
      title: "Accommodation Block",
      description: "Housing facilities for women who come to work from distant areas",
      image: "/placeholder.svg?height=300&width=400&text=Accommodation",
      category: "Support Facility",
    },
  ]

  const achievements = [
    {
      title: "Presidential Medal Ceremony",
      description: "Receiving medal from H.E. Yoweri Museveni for championing Women Economic Empowerment",
      date: "International Women's Day 2024",
      image: "/images/webImages/image.png",
      category: "National Recognition",
    },
    {
      title: "G77/NAM Expo Exhibition",
      description: "Exhibiting at the G77/NAM Side event at Serena Hotel Kigo",
      date: "2024",
      image: "/images/webImages/image (1).png",
      category: "International Expo",
    },
    {
      title: "Women's Day National Celebrations",
      description: "Participation in International Women's Day National celebrations",
      date: "March 8, 2024",
      image: "/images/webImages/image (2).png",
      category: "National Event",
    },
    {
      title: "Women in Agribusiness Expo",
      description: "Showcasing women's achievements in agribusiness",
      date: "2024",
      image: "/placeholder.svg?height=300&width=400&text=Agribusiness+Expo",
      category: "Industry Event",
    },
  ]

  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      {/* Hero Section */}
      <section className="py-12 bg-gradient-to-r from-blue-50 to-green-50">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto">
            <Badge className="bg-teal-100 text-teal-800 mb-4">Gallery</Badge>
            <h1 className="text-3xl lg:text-4xl font-serif font-bold text-gray-900 mb-4">
              Gallery
            </h1>
            <p className="text-lg text-gray-600 leading-relaxed">
              Explore the visual story of WAIB's impact through photographs capturing our milestones and community work.
            </p>
          </div>
        </div>
      </section>

      {/* Gallery Stats */}
      <section className="py-12 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-5 gap-8">
            {galleryCategories.map((category, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">{category.count}</div>
                <div className="text-sm text-gray-600">{category.title}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Gallery Tabs */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <Tabs defaultValue="dignitary-visits" className="w-full">
            <TabsList className="grid w-full grid-cols-2 md:grid-cols-5 mb-12">
              <TabsTrigger value="dignitary-visits">Dignitary Visits</TabsTrigger>
              <TabsTrigger value="community-work">Community</TabsTrigger>
              <TabsTrigger value="facilities">Facilities</TabsTrigger>
              <TabsTrigger value="achievements">Achievements</TabsTrigger>
              <TabsTrigger value="training">Training</TabsTrigger>
            </TabsList>

            {/* Dignitary Visits */}
            <TabsContent value="dignitary-visits" className="space-y-8">
              <div className="text-center mb-12">
                <h2 className="text-4xl font-serif font-bold text-gray-900 mb-4">Dignitary Visits</h2>
                <p className="text-xl text-gray-600">
                  High-profile visits from government officials and international partners
                </p>
              </div>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {dignitaryVisits.map((visit, index) => (
                  <Card key={index} className="overflow-hidden hover:shadow-xl transition-shadow group">
                    <div className="relative">
                      <Image
                        src={visit.image || "/placeholder.svg"}
                        alt={visit.title}
                        width={400}
                        height={300}
                        className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute top-4 right-4">
                        <Badge className="bg-blue-600 text-white">{visit.category}</Badge>
                      </div>
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                        <Button
                          size="sm"
                          className="opacity-0 group-hover:opacity-100 transition-opacity bg-white text-gray-900 hover:bg-gray-100"
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          View
                        </Button>
                      </div>
                    </div>
                    <CardContent className="p-6">
                      <h3 className="text-xl font-serif font-bold text-gray-900 mb-2">{visit.title}</h3>
                      <p className="text-gray-600 mb-4">{visit.description}</p>
                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4" />
                          <span>{visit.date}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MapPin className="h-4 w-4" />
                          <span>{visit.location}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Community Work */}
            <TabsContent value="community-work" className="space-y-8">
              <div className="text-center mb-12">
                <h2 className="text-4xl font-serif font-bold text-gray-900 mb-4">Community Engagement</h2>
                <p className="text-xl text-gray-600">Women at work and community activities</p>
              </div>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {communityWork.map((work, index) => (
                  <Card key={index} className="overflow-hidden hover:shadow-xl transition-shadow group">
                    <div className="relative">
                      <Image
                        src={work.image || "/placeholder.svg"}
                        alt={work.title}
                        width={400}
                        height={300}
                        className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute top-4 right-4">
                        <Badge className="bg-green-600 text-white">{work.category}</Badge>
                      </div>
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                        <div className="flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                          <Button size="sm" className="bg-white text-gray-900 hover:bg-gray-100">
                            <Eye className="mr-2 h-4 w-4" />
                            View
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            className="bg-white border-white text-gray-900 hover:bg-gray-100"
                          >
                            <Share2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                    <CardContent className="p-6">
                      <h3 className="text-xl font-serif font-bold text-gray-900 mb-2">{work.title}</h3>
                      <p className="text-gray-600">{work.description}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Facilities */}
            <TabsContent value="facilities" className="space-y-8">
              <div className="text-center mb-12">
                <h2 className="text-4xl font-serif font-bold text-gray-900 mb-4">Our Facilities</h2>
                <p className="text-xl text-gray-600">Aquaculture facilities, equipment, and infrastructure</p>
              </div>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {facilities.map((facility, index) => (
                  <Card key={index} className="overflow-hidden hover:shadow-xl transition-shadow group">
                    <div className="relative">
                      <Image
                        src={facility.image || "/placeholder.svg"}
                        alt={facility.title}
                        width={400}
                        height={300}
                        className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute top-4 right-4">
                        <Badge className="bg-teal-600 text-white">{facility.category}</Badge>
                      </div>
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                        <Button
                          size="sm"
                          className="opacity-0 group-hover:opacity-100 transition-opacity bg-white text-gray-900 hover:bg-gray-100"
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </Button>
                      </div>
                    </div>
                    <CardContent className="p-6">
                      <h3 className="text-xl font-serif font-bold text-gray-900 mb-2">{facility.title}</h3>
                      <p className="text-gray-600">{facility.description}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Achievements */}
            <TabsContent value="achievements" className="space-y-8">
              <div className="text-center mb-12">
                <h2 className="text-4xl font-serif font-bold text-gray-900 mb-4">Achievements & Awards</h2>
                <p className="text-xl text-gray-600">Recognition ceremonies and milestone celebrations</p>
              </div>
              <div className="grid md:grid-cols-2 gap-8">
                {achievements.map((achievement, index) => (
                  <Card key={index} className="overflow-hidden hover:shadow-xl transition-shadow group">
                    <div className="grid md:grid-cols-2">
                      <div className="relative">
                        <Image
                          src={achievement.image || "/placeholder.svg"}
                          alt={achievement.title}
                          width={400}
                          height={300}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                          <Button
                            size="sm"
                            className="opacity-0 group-hover:opacity-100 transition-opacity bg-white text-gray-900 hover:bg-gray-100"
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            View
                          </Button>
                        </div>
                      </div>
                      <CardContent className="p-6 flex flex-col justify-center">
                        <Badge className="bg-yellow-100 text-yellow-800 w-fit mb-4">{achievement.category}</Badge>
                        <h3 className="text-xl font-serif font-bold text-gray-900 mb-3">{achievement.title}</h3>
                        <p className="text-gray-600 mb-4">{achievement.description}</p>
                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <Calendar className="h-4 w-4" />
                          <span>{achievement.date}</span>
                        </div>
                      </CardContent>
                    </div>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Training */}
            <TabsContent value="training" className="space-y-8">
              <div className="text-center mb-12">
                <h2 className="text-4xl font-serif font-bold text-gray-900 mb-4">Training & Capacity Building</h2>
                <p className="text-xl text-gray-600">Skills development and educational programs</p>
              </div>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {Array.from({ length: 6 }, (_, index) => (
                  <Card key={index} className="overflow-hidden hover:shadow-xl transition-shadow group">
                    <div className="relative">
                      <Image
                        src={`/placeholder.svg?height=300&width=400&text=Training+${index + 1}`}
                        alt={`Training Session ${index + 1}`}
                        width={400}
                        height={300}
                        className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute top-4 right-4">
                        <Badge className="bg-purple-600 text-white">Training</Badge>
                      </div>
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                        <Button
                          size="sm"
                          className="opacity-0 group-hover:opacity-100 transition-opacity bg-white text-gray-900 hover:bg-gray-100"
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          View
                        </Button>
                      </div>
                    </div>
                    <CardContent className="p-6">
                      <h3 className="text-xl font-serif font-bold text-gray-900 mb-2">
                        {
                          [
                            "Fish Farming Techniques",
                            "Value Chain Training",
                            "Business Skills",
                            "Quality Control",
                            "Marketing Strategies",
                            "Financial Literacy",
                          ][index]
                        }
                      </h3>
                      <p className="text-gray-600">
                        {
                          [
                            "Learning modern fish farming techniques",
                            "Understanding the complete value chain",
                            "Developing business management skills",
                            "Ensuring fish quality standards",
                            "Effective marketing and sales",
                            "Managing finances and savings",
                          ][index]
                        }
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* Download Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-3xl font-serif font-bold text-gray-900 mb-6">Download Our Company Profile</h2>
            <p className="text-lg text-gray-600 mb-8">
              Get the complete story of WAIB's journey, impact, and future plans in our comprehensive company profile
              document.
            </p>
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white">
              <Download className="mr-2 h-5 w-5" />
              Download Company Profile (PDF)
            </Button>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-green-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-serif font-bold mb-6">Share Our Story</h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Help us spread awareness about women's empowerment through aquaculture. Share our gallery and inspire others
            to support sustainable development.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
              <Heart className="mr-2 h-5 w-5" />
              Support Our Work
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-white text-white hover:bg-white hover:text-blue-600 bg-transparent"
            >
              <Globe className="mr-2 h-5 w-5" />
              Partner with Us
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
