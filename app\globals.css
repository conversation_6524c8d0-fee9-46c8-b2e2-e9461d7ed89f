@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Enhanced Typography System with Serif Titles */
/*
  TYPOGRAPHY DESIGN SYSTEM:

  Primary Fonts:
  - Playfair Display: Elegant serif for all titles and headings
  - Crimson Text: Alternative serif for special cases
  - Plus Jakarta Sans: Clean sans-serif for body text
  - Space Grotesk: Modern sans-serif for UI elements
  - JetBrains Mono: Monospace for code

  Usage Guidelines:
  - Use .font-serif or title utility classes for ALL headings (h1-h6)
  - Use .font-body or default for body text
  - Use .font-display for navigation and UI elements
  - Use .font-mono for code snippets

  Title Classes:
  - .title-hero: Main page headlines (h1)
  - .title-section: Section headers (h2)
  - .title-subsection: Subsection headers (h3)
  - .title-card: Card titles (h4)
  - .title-small: Small headings (h5, h6)
*/
@import url("https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@200;300;400;500;600;700;800&family=Space+Grotesk:wght@300;400;500;600;700&family=JetBrains+Mono:wght@300;400;500;600&family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&family=Crimson+Text:ital,wght@0,400;0,600;0,700;1,400;1,600;1,700&display=swap");

body {
  font-family: "Plus Jakarta Sans", system-ui, -apple-system, sans-serif;
  font-optical-sizing: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.font-display {
  font-family: "Space Grotesk", system-ui, sans-serif;
}

.font-mono {
  font-family: "JetBrains Mono", monospace;
}

/* Serif font classes for titles */
.font-serif {
  font-family: "Playfair Display", "Times New Roman", serif;
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
}

.font-serif-alt {
  font-family: "Crimson Text", "Times New Roman", serif;
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
}

/* Title-specific classes with serif fonts */
.title-primary {
  font-family: "Playfair Display", "Times New Roman", serif;
  font-weight: 700;
  letter-spacing: -0.025em;
  line-height: 1.1;
}

.title-secondary {
  font-family: "Playfair Display", "Times New Roman", serif;
  font-weight: 600;
  letter-spacing: -0.02em;
  line-height: 1.2;
}

.title-tertiary {
  font-family: "Playfair Display", "Times New Roman", serif;
  font-weight: 600;
  letter-spacing: -0.015em;
  line-height: 1.3;
}

/* Comprehensive title utility classes */
.title-hero {
  @apply font-serif font-bold text-6xl lg:text-8xl leading-tight;
  letter-spacing: -0.03em;
}

.title-section {
  @apply font-serif font-bold text-4xl lg:text-5xl leading-tight;
  letter-spacing: -0.025em;
}

.title-subsection {
  @apply font-serif font-bold text-2xl lg:text-3xl leading-tight;
  letter-spacing: -0.02em;
}

.title-card {
  @apply font-serif font-semibold text-xl lg:text-2xl leading-tight;
  letter-spacing: -0.015em;
}

.title-small {
  @apply font-serif font-semibold text-lg leading-tight;
  letter-spacing: -0.01em;
}

/* Responsive title classes */
.title-responsive-hero {
  @apply font-serif font-bold leading-tight;
  font-size: clamp(2.5rem, 8vw, 6rem);
  letter-spacing: -0.03em;
}

.title-responsive-section {
  @apply font-serif font-bold leading-tight;
  font-size: clamp(2rem, 6vw, 4rem);
  letter-spacing: -0.025em;
}

.title-responsive-subsection {
  @apply font-serif font-bold leading-tight;
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  letter-spacing: -0.02em;
}

/* Custom Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes floatDelayed {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

@keyframes morphing {
  0%,
  100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  }
  50% {
    border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
  opacity: 0;
}

.animate-fade-in-right {
  animation: fadeInRight 0.8s ease-out forwards;
  opacity: 0;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out forwards;
  opacity: 0;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: floatDelayed 8s ease-in-out infinite;
  animation-delay: -2s;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.animate-morphing {
  animation: morphing 8s ease-in-out infinite;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #3b82f6, #10b981);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #2563eb, #059669);
}

/* Enhanced Glassmorphism utilities */
.glass {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.25);
}

.glass-dark {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.glass-card {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Creative Neumorphism utilities */
.neomorphism {
  background: #f0f0f0;
  box-shadow: 20px 20px 60px #d1d1d1, -20px -20px 60px #ffffff;
}

.neomorphism-inset {
  background: #f0f0f0;
  box-shadow: inset 20px 20px 60px #d1d1d1, inset -20px -20px 60px #ffffff;
}

/* Modern button styles */
.btn-modern {
  @apply relative overflow-hidden rounded-full px-8 py-4 font-semibold transition-all duration-300;
  @apply bg-gradient-to-r from-blue-600 to-blue-700 text-white;
  @apply hover:from-blue-700 hover:to-blue-800 hover:shadow-lg hover:scale-105;
  @apply active:scale-95;
}

.btn-modern::before {
  content: "";
  @apply absolute inset-0 bg-white opacity-0 transition-opacity duration-300;
}

.btn-modern:hover::before {
  @apply opacity-10;
}

/* Enhanced Card hover effects */
.card-hover {
  @apply transition-all duration-500 ease-out;
  @apply hover:shadow-2xl hover:shadow-blue-500/20 hover:-translate-y-3;
}

.card-glass {
  @apply backdrop-blur-md bg-white/85 border border-white/30 rounded-3xl;
  @apply shadow-xl shadow-black/10;
  @apply hover:bg-white/95 hover:shadow-2xl hover:shadow-black/15;
  @apply transition-all duration-500 ease-out;
}

.card-creative {
  @apply relative overflow-hidden rounded-3xl;
  @apply before:absolute before:inset-0 before:bg-gradient-to-br before:from-white/20 before:to-transparent before:opacity-0;
  @apply hover:before:opacity-100 before:transition-opacity before:duration-300;
}

/* Text gradient */
.text-gradient {
  @apply bg-gradient-to-r from-blue-600 via-purple-600 to-green-600 bg-clip-text text-transparent;
}

.text-gradient-blue {
  @apply bg-gradient-to-r from-blue-500 to-cyan-500 bg-clip-text text-transparent;
}

.text-gradient-green {
  @apply bg-gradient-to-r from-green-500 to-teal-500 bg-clip-text text-transparent;
}

/* Creative backgrounds */
.bg-mesh {
  background-image: radial-gradient(at 40% 20%, hsla(228, 100%, 74%, 0.1) 0px, transparent 50%),
    radial-gradient(at 80% 0%, hsla(189, 100%, 56%, 0.1) 0px, transparent 50%),
    radial-gradient(at 0% 50%, hsla(355, 100%, 93%, 0.1) 0px, transparent 50%),
    radial-gradient(at 80% 50%, hsla(340, 100%, 76%, 0.1) 0px, transparent 50%),
    radial-gradient(at 0% 100%, hsla(22, 100%, 77%, 0.1) 0px, transparent 50%),
    radial-gradient(at 80% 100%, hsla(242, 100%, 70%, 0.1) 0px, transparent 50%),
    radial-gradient(at 0% 0%, hsla(343, 100%, 76%, 0.1) 0px, transparent 50%);
}

/* Loading states */
.skeleton {
  @apply animate-pulse bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded;
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Focus states */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-white;
}

/* Creative shapes */
.shape-blob {
  border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
}

.shape-blob-animated {
  border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  animation: morphing 8s ease-in-out infinite;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .text-responsive-xl {
    @apply text-2xl;
  }
  .text-responsive-2xl {
    @apply text-3xl;
  }
  .text-responsive-3xl {
    @apply text-4xl;
  }
  .text-responsive-4xl {
    @apply text-5xl;
  }
}

@media (min-width: 641px) {
  .text-responsive-xl {
    @apply text-3xl;
  }
  .text-responsive-2xl {
    @apply text-4xl;
  }
  .text-responsive-3xl {
    @apply text-5xl;
  }
  .text-responsive-4xl {
    @apply text-6xl;
  }
}

@media (min-width: 1024px) {
  .text-responsive-xl {
    @apply text-4xl;
  }
  .text-responsive-2xl {
    @apply text-5xl;
  }
  .text-responsive-3xl {
    @apply text-6xl;
  }
  .text-responsive-4xl {
    @apply text-7xl;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .card-glass {
    @apply bg-white border-gray-300;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
