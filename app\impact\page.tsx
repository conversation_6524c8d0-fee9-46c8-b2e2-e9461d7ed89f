import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Award,
  Users,
  TrendingUp,
  Heart,
  Fish,
  DollarSign,
  Home,
  Briefcase,
  Star,
  Globe,
  Target,
  CheckCircle,
  Sparkles,
  ArrowUp,
  BarChart3,
} from "lucide-react"
import Image from "next/image"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"
import ScrollReveal from "@/components/scroll-reveal"
import GlassCard from "@/components/glass-card"
import AnimatedCounter from "@/components/animated-counter"
import FloatingElements from "@/components/floating-elements"

export default function ImpactPage() {
  const impactStats = [
    {
      icon: Fish,
      value: 394.44,
      unit: "Tons",
      label: "Fish Produced",
      color: "blue",
      description: "Sustainable fish production",
    },
    { icon: Users, value: 1400, unit: "+", label: "Women Targeted", color: "green", description: "Lives transformed" },
    {
      icon: DollarSign,
      value: 1000,
      unit: "",
      label: "Women Paid Dividends",
      color: "teal",
      description: "Economic empowerment",
    },
    {
      icon: TrendingUp,
      value: 100,
      unit: "K",
      label: "Average Dividend (UGX)",
      color: "purple",
      description: "Financial independence",
    },
  ]

  const transformationAreas = [
    {
      title: "Economic Empowerment",
      description: "Women started small businesses and gained financial independence",
      progress: 85,
      icon: TrendingUp,
      color: "blue",
      impact: "1,200+ women now have independent income sources",
    },
    {
      title: "Poverty Alleviation",
      description: "Women now offer paid labor and contribute to household income",
      progress: 78,
      icon: DollarSign,
      color: "green",
      impact: "Average household income increased by 60%",
    },
    {
      title: "Food & Nutrition Security",
      description: "Increased agricultural productivity and enhanced nutrition in households",
      progress: 92,
      icon: Home,
      color: "teal",
      impact: "95% of families report improved nutrition",
    },
    {
      title: "Gender Relations",
      description: "Improved gender relations and reduced domestic violence",
      progress: 70,
      icon: Heart,
      color: "purple",
      impact: "40% reduction in reported domestic violence cases",
    },
    {
      title: "Cultural Transformation",
      description: "Women now work on the lake - a significant cultural shift",
      progress: 88,
      icon: Users,
      color: "orange",
      impact: "Cultural barriers broken in 15+ communities",
    },
    {
      title: "Skills Development",
      description: "Women gained skills along the entire fish value chain",
      progress: 95,
      icon: Target,
      color: "pink",
      impact: "100% of participants gained new technical skills",
    },
  ]

  const achievements = [
    {
      title: "Presidential Recognition",
      description:
        "Received a medal from H.E. Yoweri Museveni for championing Women Economic Empowerment during International Women's Day National celebrations 2024",
      icon: Award,
      color: "yellow",
      year: "2024",
      category: "National Recognition",
    },
    {
      title: "UN Women Partnership",
      description:
        "Ongoing collaboration with UN Women Uganda, including visits from country representatives and program officers for monitoring and support",
      icon: Globe,
      color: "blue",
      year: "2019-Present",
      category: "International Partnership",
    },
    {
      title: "G77/NAM Expo Participation",
      description:
        "Exhibited at the G77/NAM Side event at Serena Hotel Kigo in 2024, showcasing our impact on women's empowerment",
      icon: Star,
      color: "green",
      year: "2024",
      category: "International Expo",
    },
    {
      title: "Government Partnerships",
      description:
        "Strong partnerships with government ministries including visits from the Commissioner for Fisheries and Minister of State for Fisheries",
      icon: Briefcase,
      color: "teal",
      year: "2019-Present",
      category: "Government Relations",
    },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 relative overflow-hidden">
      <FloatingElements />
      <Navbar />

      {/* Hero Section with Dynamic Background */}
      <section className="relative h-64 flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-green-900/90 via-teal-800/80 to-blue-800/90" />
          <div
            className="absolute inset-0 bg-cover bg-center transform scale-110"
            style={{
              backgroundImage: `url('/images/webImages/Q7.jpg')`,
            }}
          />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center max-w-3xl mx-auto text-white">
            <ScrollReveal>
              <Badge className="bg-white/20 backdrop-blur-sm text-white border-white/30 px-4 py-2 text-sm font-semibold mb-4">
                Our Impact
              </Badge>
            </ScrollReveal>

            <ScrollReveal delay={200}>
              <h1 className="text-3xl lg:text-4xl font-serif font-bold mb-4 leading-tight">
                Our Impact
              </h1>
            </ScrollReveal>

            <ScrollReveal delay={400}>
              <p className="text-lg text-green-100 leading-relaxed max-w-2xl mx-auto">
                Discover how WAIB is creating lasting change in Bugiri District through sustainable aquaculture.
              </p>
            </ScrollReveal>
          </div>
        </div>
      </section>

      {/* Impact Statistics - Enhanced Design */}
      <section className="py-32 relative">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <div className="text-center mb-20">
              <Badge className="bg-gradient-to-r from-green-100 to-teal-100 text-green-800 border-0 px-6 py-2 text-sm font-semibold mb-6">
                Impact by the Numbers
              </Badge>
              <h2 className="text-5xl font-serif font-bold text-gray-900 mb-6">
                Measurable Results That
                <span className="block bg-gradient-to-r from-green-600 to-teal-600 bg-clip-text text-transparent">
                  Speak to Our Commitment
                </span>
              </h2>
            </div>
          </ScrollReveal>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {impactStats.map((stat, index) => (
              <ScrollReveal key={index} delay={index * 150} direction="up">
                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-br from-white to-gray-50 rounded-3xl shadow-2xl group-hover:shadow-3xl transition-all duration-500 transform group-hover:scale-105" />
                  <div className="relative p-8 text-center">
                    <div
                      className={`w-20 h-20 bg-gradient-to-br from-${stat.color}-100 to-${stat.color}-200 rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 group-hover:rotate-12 transition-all duration-500`}
                    >
                      <stat.icon className={`h-10 w-10 text-${stat.color}-600`} />
                    </div>
                    <div className={`text-4xl font-bold text-${stat.color}-600 mb-2`}>
                      <AnimatedCounter end={stat.value} suffix={stat.unit} />
                    </div>
                    <h3 className="text-lg font-serif font-semibold text-gray-900 mb-2">{stat.label}</h3>
                    <p className="text-sm text-gray-600">{stat.description}</p>
                    <div className="mt-4">
                      <ArrowUp className={`h-4 w-4 text-${stat.color}-500 mx-auto animate-bounce`} />
                    </div>
                  </div>
                </div>
              </ScrollReveal>
            ))}
          </div>
        </div>
      </section>

      {/* Transformation Areas - Creative Layout */}
      <section className="py-32 relative">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-50/50 to-blue-50/50" />
        <div className="container mx-auto px-4 relative">
          <ScrollReveal>
            <div className="text-center mb-20">
              <Badge className="bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 border-0 px-6 py-2 text-sm font-semibold mb-6">
                Areas of Transformation
              </Badge>
              <h2 className="text-5xl font-serif font-bold text-gray-900 mb-6">
                How We're Creating
                <span className="block bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Lasting Change
                </span>
              </h2>
            </div>
          </ScrollReveal>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {transformationAreas.map((area, index) => (
              <ScrollReveal key={index} delay={index * 100} direction="up">
                <GlassCard className="p-8 group h-full">
                  <div className="flex items-center space-x-4 mb-6">
                    <div
                      className={`w-16 h-16 bg-gradient-to-br from-${area.color}-100 to-${area.color}-200 rounded-2xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-6 transition-all duration-300`}
                    >
                      <area.icon className={`h-8 w-8 text-${area.color}-600`} />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-serif font-bold text-gray-900 mb-2">{area.title}</h3>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-gray-700">{area.progress}%</span>
                        <div className="flex-1">
                          <Progress value={area.progress} className="h-2" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <p className="text-gray-600 mb-4 leading-relaxed">{area.description}</p>
                  <div className={`p-4 bg-${area.color}-50 rounded-xl border border-${area.color}-100`}>
                    <p className={`text-sm font-medium text-${area.color}-700`}>{area.impact}</p>
                  </div>
                </GlassCard>
              </ScrollReveal>
            ))}
          </div>
        </div>
      </section>

      {/* Achievements - Timeline Style */}
      <section className="py-32 relative">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <div className="text-center mb-20">
              <Badge className="bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-800 border-0 px-6 py-2 text-sm font-semibold mb-6">
                Recognition & Achievements
              </Badge>
              <h2 className="text-5xl font-serif font-bold text-gray-900 mb-6">
                Celebrating Our
                <span className="block bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent">
                  Milestones & Partnerships
                </span>
              </h2>
            </div>
          </ScrollReveal>

          <div className="grid md:grid-cols-2 gap-8">
            {achievements.map((achievement, index) => (
              <ScrollReveal key={index} delay={index * 200} direction={index % 2 === 0 ? "left" : "right"}>
                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-br from-white to-gray-50 rounded-3xl shadow-2xl group-hover:shadow-3xl transition-all duration-500 transform group-hover:-rotate-1" />
                  <div className="relative p-8">
                    <div className="flex items-start space-x-6">
                      <div
                        className={`w-20 h-20 bg-gradient-to-br from-${achievement.color}-100 to-${achievement.color}-200 rounded-2xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 group-hover:rotate-12 transition-all duration-500`}
                      >
                        <achievement.icon className={`h-10 w-10 text-${achievement.color}-600`} />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-3">
                          <Badge className={`bg-${achievement.color}-100 text-${achievement.color}-800`}>
                            {achievement.category}
                          </Badge>
                          <span className="text-sm text-gray-500">{achievement.year}</span>
                        </div>
                        <h3 className="text-2xl font-serif font-bold text-gray-900 mb-4">{achievement.title}</h3>
                        <p className="text-gray-600 leading-relaxed">{achievement.description}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </ScrollReveal>
            ))}
          </div>
        </div>
      </section>

      {/* Future Goals */}
      <section className="py-32 relative">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50/50 to-green-50/50" />
        <div className="container mx-auto px-4 relative">
          <div className="grid lg:grid-cols-2 gap-20 items-center">
            <ScrollReveal direction="left">
              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-200 to-green-200 rounded-3xl blur-2xl opacity-30 group-hover:opacity-50 transition-opacity duration-500" />
                <Image
                  src="/images/webImages/Q8.jpg"
                  alt="Future Vision"
                  width={700}
                  height={600}
                  className="rounded-3xl shadow-2xl relative z-10 group-hover:scale-105 transition-transform duration-500"
                />
                <div className="absolute -bottom-8 -right-8 z-20">
                  <GlassCard className="p-6 text-center">
                    <div className="text-3xl font-bold text-green-600">2030</div>
                    <div className="text-sm text-gray-600">Vision Year</div>
                  </GlassCard>
                </div>
              </div>
            </ScrollReveal>

            <ScrollReveal direction="right" delay={200}>
              <div className="space-y-8">
                <div>
                  <Badge className="bg-gradient-to-r from-green-100 to-teal-100 text-green-800 border-0 px-4 py-2 mb-6">
                    Looking Ahead
                  </Badge>
                  <h2 className="text-4xl font-serif font-bold text-gray-900 mb-6 leading-tight">
                    Our Vision Extends Beyond
                    <span className="block bg-gradient-to-r from-green-600 to-teal-600 bg-clip-text text-transparent">
                      Current Achievements
                    </span>
                  </h2>
                  <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                    By 2025, we aim to ensure that women have income security, decent work, and economic autonomy. By
                    2030, WAIB will be a recognized center of excellence for women's aquaculture empowerment.
                  </p>
                </div>

                <div className="space-y-6">
                  {[
                    { text: "Expand to accommodate 2,000 women by 2025", progress: 60 },
                    { text: "Establish land-based recirculating aquaculture system", progress: 30 },
                    { text: "Build fish processing plant and ice plant facilities", progress: 25 },
                    { text: "Create regional and international market access", progress: 45 },
                  ].map((goal, index) => (
                    <div key={index} className="flex items-start space-x-4">
                      <CheckCircle className="h-6 w-6 text-green-600 mt-1 flex-shrink-0" />
                      <div className="flex-1">
                        <p className="text-gray-700 mb-2">{goal.text}</p>
                        <div className="flex items-center space-x-3">
                          <Progress value={goal.progress} className="flex-1 h-2" />
                          <span className="text-sm text-gray-500">{goal.progress}%</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <Button className="bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 text-white px-8 py-4 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group">
                  <Target className="mr-2 h-5 w-5 group-hover:rotate-12 transition-transform duration-300" />
                  Join Our Vision
                </Button>
              </div>
            </ScrollReveal>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-green-600 via-teal-600 to-blue-600" />
        <div className="absolute inset-0 bg-black/20" />
        <div className="container mx-auto px-4 relative z-10">
          <ScrollReveal>
            <div className="text-center text-white">
              <h2 className="text-5xl font-serif font-bold mb-8">Be Part of Our Impact</h2>
              <p className="text-xl mb-12 max-w-4xl mx-auto leading-relaxed opacity-90">
                Join us in creating even greater impact. Your support can help us reach more women and transform more
                communities through sustainable aquaculture.
              </p>
              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <Button
                  size="lg"
                  className="bg-white text-green-600 hover:bg-gray-100 px-8 py-4 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group"
                >
                  <Heart className="mr-2 h-5 w-5 group-hover:animate-pulse" />
                  Support Our Work
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white/30 text-white hover:bg-white/10 backdrop-blur-sm px-8 py-4 rounded-full transition-all duration-300 hover:scale-105 bg-transparent"
                >
                  <Globe className="mr-2 h-5 w-5" />
                  Partner with Us
                </Button>
              </div>
            </div>
          </ScrollReveal>
        </div>
      </section>

      <Footer />
    </div>
  )
}
